#include <Windows.h>
#include <map>

#include "drv.h"

int main()
{
	printf("=== 驱动开发：内核取应用层模块基地址 ===\n");
	printf("基于LyShark博客文章实现的64位版本\n\n");

	eneio_lib driver;

	printf("[*] 正在枚举所有进程...\n");
	printf("%-8s %-40s %-18s\n", "PID", "进程名", "基址");
	printf("%-8s %-40s %-18s\n", "----", "----------------------------------------", "------------------");

	// 枚举所有进程
	driver.enumerate_all_processes();

	printf("\n[*] 测试获取特定进程基址...\n");

	// 测试获取进程基址 - 长进程名问题演示
	const char* target_process = "内存工具aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa.exe";
	printf("[*] 正在获取进程 %s 的基址...\n", target_process);
	printf("[!] 注意：由于Windows内核限制，EPROCESS.ImageFileName只有15个字符\n");

	uintptr_t base = driver.get_process_base(target_process);

	if (!base)
	{
		printf("[-] 直接匹配失败，尝试通过PID获取...\n");

		// 尝试通过PID获取（使用用户态API获取完整进程名）
		DWORD pid = driver.get_process_id(target_process);
		if (pid > 0)
		{
			printf("[*] 找到PID: %d\n", pid);
			base = driver.get_process_base_by_pid(pid);
			if (base)
			{
				printf("[+] 通过PID %d 获取到基址: 0x%llx\n", pid, base);
			}
		}

		if (!base)
		{
			printf("[-] PID方法也失败，尝试智能路径匹配...\n");
			base = driver.get_process_base_by_full_path(target_process);
		}

		if (!base)
		{
			printf("[-] 所有方法都失败了\n");
			printf("[*] 提示：对于长进程名，建议使用以下方法之一：\n");
			printf("    1. 使用进程的短名字（前15个字符）\n");
			printf("    2. 使用PID直接获取\n");
			printf("    3. 从进程列表中找到对应的PID\n");
			printf("    4. 使用智能路径匹配功能\n");
			system("pause");
			return false;
		}
	}
	else
	{
		printf("[+] %s 基址: 0x%llx\n", target_process, base);
	}

	// 验证进程基址（读取PE头）
	UINT8 pe_header[2] = { 0 };
	if (driver.read_virtual_memory(base, pe_header, 2))
	{
		if (pe_header[0] == 0x4D && pe_header[1] == 0x5A) // MZ header
		{
			printf("[+] PE头验证成功 (MZ: 0x%02x 0x%02x)\n", pe_header[0], pe_header[1]);
		}
		else
		{
			printf("[-] PE头验证失败\n");
		}
	}

	printf("\n[*] 正在获取模块基址...\n");

	// 测试获取常见模块基址
	const char* modules[] = {
		"kernel32.dll",
		"ntdll.dll",
		"user32.dll",
		"advapi32.dll"
	};

	for (int i = 0; i < sizeof(modules) / sizeof(modules[0]); i++)
	{
		uintptr_t module_base = driver.get_module_base(target_process, modules[i]);
		if (module_base)
		{
			printf("[+] %s 基址: 0x%llx\n", modules[i], module_base);

			// 验证模块基址（读取PE头）
			UINT8 module_pe[2] = { 0 };
			if (driver.read_virtual_memory(module_base, module_pe, 2))
			{
				if (module_pe[0] == 0x4D && module_pe[1] == 0x5A)
				{
					printf("    └─ PE头验证成功\n");
				}
				else
				{
					printf("    └─ PE头验证失败\n");
				}
			}
		}
		else
		{
			printf("[-] 获取 %s 基址失败\n", modules[i]);
		}
	}

	printf("\n[*] 演示长进程名处理方法...\n");

	// 演示不同的长进程名处理方法
	const char* long_process_examples[] = {
		"verylongprocessname.exe",
		"C:\\Program Files\\Very Long Application Name\\app.exe",
		"内存工具aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa.exe"
	};

	for (int i = 0; i < sizeof(long_process_examples) / sizeof(long_process_examples[0]); i++)
	{
		printf("\n[*] 测试长进程名: %s\n", long_process_examples[i]);

		// 方法1：直接匹配
		uintptr_t base1 = driver.get_process_base(long_process_examples[i]);
		printf("    直接匹配: %s\n", base1 ? "成功" : "失败");

		// 方法2：通过PID
		DWORD pid = driver.get_process_id(long_process_examples[i]);
		if (pid > 0)
		{
			uintptr_t base2 = driver.get_process_base_by_pid(pid);
			printf("    PID方法: %s (PID: %d)\n", base2 ? "成功" : "失败", pid);
		}
		else
		{
			printf("    PID方法: 失败 (未找到PID)\n");
		}

		// 方法3：智能路径匹配
		uintptr_t base3 = driver.get_process_base_by_full_path(long_process_examples[i]);
		printf("    智能匹配: %s\n", base3 ? "成功" : "失败");
	}

	printf("\n[+] 所有测试完成！\n");
	printf("\n[*] 长进程名处理总结：\n");
	printf("    1. Windows内核EPROCESS.ImageFileName字段限制为15个字符\n");
	printf("    2. 超过15个字符的进程名会被截断\n");
	printf("    3. 本程序提供了多种解决方案来处理这个问题\n");
	printf("    4. 建议优先使用PID方法或智能匹配方法\n");

	printf("\n按任意键退出...\n");
	system("pause");

	return true;
}