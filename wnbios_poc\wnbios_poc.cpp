#include <Windows.h>
#include <map>

#include "drv.h"

int main()
{
	printf("=== 驱动开发：内核取应用层模块基地址 ===\n");
	printf("基于LyShark博客文章实现的64位版本\n\n");

	eneio_lib driver;

	printf("[*] 正在枚举所有进程...\n");
	printf("%-8s %-40s %-18s\n", "PID", "进程名", "基址");
	printf("%-8s %-40s %-18s\n", "----", "----------------------------------------", "------------------");

	// 枚举所有进程
	//driver.enumerate_all_processes();

	printf("\n[*] 测试获取特定进程基址...\n");

	// 测试获取进程基址 - 长进程名问题演示
	const char* target_process = "DeltaForceClient-Win64-Shipping.exe";
	printf("[*] 正在获取进程 %s 的基址...\n", target_process);
	printf("[!] 注意：由于Windows内核限制，EPROCESS.ImageFileName只有15个字符\n");

	uintptr_t base = driver.get_process_base(target_process);

	if (!base)
	{
		printf("[-] 直接匹配失败，使用PID方法...\n");

		// 使用PID方法获取（这是处理长进程名的最佳方法）
		DWORD pid = driver.get_process_id(target_process);
		if (pid > 0)
		{
			printf("[*] 找到PID: %d\n", pid);
			base = driver.get_process_base_by_pid(pid);
			if (base)
			{
				printf("[+] 通过PID %d 获取到基址: 0x%llx\n", pid, base);
			}
			else
			{
				printf("[-] 通过PID获取基址失败\n");
			}
		}
		else
		{
			printf("[-] 未找到对应的PID\n");
		}

		if (!base)
		{
			printf("[-] PID方法失败\n");
			printf("[*] 提示：对于长进程名问题：\n");
			printf("    1. Windows内核EPROCESS.ImageFileName限制为15字符\n");
			printf("    2. 建议使用PID方法绕过此限制\n");
			printf("    3. 或使用进程的短名字（前15个字符）\n");
			system("pause");
			return false;
		}
	}
	else
	{
		printf("[+] 直接匹配成功，基址: 0x%llx\n", base);
	}

	// 验证进程基址（读取PE头）
	UINT8 pe_header[2] = { 0 };
	if (driver.read_virtual_memory(base, pe_header, 2))
	{
		if (pe_header[0] == 0x4D && pe_header[1] == 0x5A) // MZ header
		{
			printf("[+] PE头验证成功 (MZ: 0x%02x 0x%02x)\n", pe_header[0], pe_header[1]);
		}
		else
		{
			printf("[-] PE头验证失败\n");
		}
	}

	printf("\n[*] 正在获取模块基址...\n");

	// 测试获取常见模块基址
	const char* modules[] = {
		"kernel32.dll",
		"ntdll.dll",
		"user32.dll",
		"advapi32.dll"
	};

	for (int i = 0; i < sizeof(modules) / sizeof(modules[0]); i++)
	{
		uintptr_t module_base = driver.get_module_base(target_process, modules[i]);
		if (module_base)
		{
			printf("[+] %s 基址: 0x%llx\n", modules[i], module_base);

			// 验证模块基址（读取PE头）
			UINT8 module_pe[2] = { 0 };
			if (driver.read_virtual_memory(module_base, module_pe, 2))
			{
				if (module_pe[0] == 0x4D && module_pe[1] == 0x5A)
				{
					printf("    └─ PE头验证成功\n");
				}
				else
				{
					printf("    └─ PE头验证失败\n");
				}
			}
		}
		else
		{
			printf("[-] 获取 %s 基址失败\n", modules[i]);
		}
	}

	printf("\n[*] 演示PID方法处理长进程名...\n");

	// 演示使用PID方法处理长进程名
	const char* long_process_example = "DeltaForceClient-Win64-Shipping.exe";

	printf("\n[*] 测试长进程名: %s\n", long_process_example);
	printf("[*] 进程名长度: %zu 字符 (超过15字符限制)\n", strlen(long_process_example));

	// 只使用PID方法
	printf("\n[*] 使用PID方法获取进程基址...\n");
	DWORD pid = driver.get_process_id(long_process_example);
	if (pid > 0)
	{
		printf("[+] 找到进程PID: %d\n", pid);
		uintptr_t base_by_pid = driver.get_process_base_by_pid(pid);
		if (base_by_pid)
		{
			printf("[+] 通过PID成功获取基址: 0x%llx\n", base_by_pid);

			// 验证基址
			UINT8 pe_header[2] = { 0 };
			if (driver.read_virtual_memory(base_by_pid, pe_header, 2))
			{
				if (pe_header[0] == 0x4D && pe_header[1] == 0x5A)
				{
					printf("[+] PE头验证成功\n");
				}
				else
				{
					printf("[-] PE头验证失败\n");
				}
			}
		}
		else
		{
			printf("[-] 通过PID获取基址失败\n");
		}
	}
	else
	{
		printf("[-] 未找到对应的PID\n");
	}

	printf("\n[+] 所有测试完成！\n");
	printf("\n[*] 长进程名处理总结：\n");
	printf("    1. Windows内核EPROCESS.ImageFileName字段限制为15个字符\n");
	printf("    2. 超过15个字符的进程名会被截断\n");
	printf("    3. PID方法是处理长进程名的最佳解决方案\n");
	printf("    4. 用户态API可以获取完整进程名，然后通过PID在内核中定位\n");

	printf("\n按任意键退出...\n");
	system("pause");

	return true;
}